<template>
  <div id="rose-chart">
    <div class="rose-chart-title">近一个月进出情况</div>
    <dv-charts :option="option" />
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  data () {
    return {
      option: {}
    }
  },
  methods: {
    createData () {
      // 生成近30天的日期数据
      const days = []
      const entryData = []
      const exitData = []

      for (let i = 29; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        days.push(`${date.getDate()}日`)

        // 模拟进出人数数据，实际项目中应该从API获取
        entryData.push(Math.floor(Math.random() * 500) + 1500) // 进入人数 1500-2000
        exitData.push(Math.floor(Math.random() * 500) + 1400) // 出去人数 1400-1900
      }

      this.option = {
        series: [
          {
            type: 'line',
            name: '进门人次',
            data: entryData,
            lineStyle: {
              stroke: '#1e88e5',
              lineWidth: 3
            },
            pointStyle: {
              fill: '#1e88e5',
              r: 4
            },
            smooth: true
          },
          {
            type: 'line',
            name: '出门人次',
            data: exitData,
            lineStyle: {
              stroke: '#00bcd4',
              lineWidth: 3
            },
            pointStyle: {
              fill: '#00bcd4',
              r: 4
            },
            smooth: true
          }
        ],
        xAxis: {
          name: '日期',
          data: days,
          nameTextStyle: {
            fill: '#fff',
            fontSize: 14
          },
          axisLabel: {
            fill: '#fff',
            fontSize: 11
          },
          axisLine: {
            style: {
              stroke: '#03d3ec'
            }
          }
        },
        yAxis: {
          name: '人次',
          nameTextStyle: {
            fill: '#fff',
            fontSize: 14
          },
          axisLabel: {
            fill: '#fff',
            fontSize: 12
          },
          axisLine: {
            style: {
              stroke: '#03d3ec'
            }
          },
          splitLine: {
            style: {
              stroke: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        legend: {
          show: true,
          orient: 'horizontal',
          left: 'center',
          bottom: 10,
          itemGap: 20,
          textStyle: {
            fill: '#fff',
            fontSize: 12
          },
          iconStyle: {
            lineWidth: 3
          }
        }
      }
    }
  },
  mounted () {
    const { createData } = this

    createData()

    setInterval(createData, 30000)
  }
}
</script>

<style lang="less">
#rose-chart {
  width: 100%;
  height: 100%;
  background-color: rgba(6, 30, 93, 0.5);
  border-top: 2px solid rgba(1, 153, 209, .5);
  border-radius: 15px;
  box-sizing: border-box;

  .rose-chart-title {
    height: 50px;
    font-weight: bold;
    text-indent: 20px;
    font-size: 18px;
    display: flex;
    align-items: center;
    color: #ffffff;
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  }

  .dv-charts-container {
    height: calc(~"100% - 50px");
  }
}
</style>
